# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src'
whitelisted_packages = 'cpp_version'.split(';') if 'cpp_version' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel;/opt/ros/noetic' != '' else []
