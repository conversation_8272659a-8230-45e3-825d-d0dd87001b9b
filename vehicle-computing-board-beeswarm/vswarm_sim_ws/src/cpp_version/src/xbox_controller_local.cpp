// Xbox手柄本地控制器 - 只控制当前主机的车辆
// 类似于Set_Car_Model_State_Simple.cpp，但使用手柄输入而不是键盘输入
// 增强版：支持interactive_led_control.py中的所有LED功能
#include <iostream>
#include <string>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <ros/ros.h>
#include <sensor_msgs/Joy.h>
#include <geometry_msgs/Twist.h>
#include <std_msgs/Int32.h>

class XboxControllerLocal {
public:
    XboxControllerLocal() {
        // 获取主机名作为模型名
        char hostname[256];
        gethostname(hostname, sizeof(hostname));
        model_name_ = std::string(hostname);
        
        // 初始化ROS
        ros::NodeHandle nh;
        
        // 创建发布者 - 直接发布到本地车辆的话题
        vel_pub_ = nh.advertise<geometry_msgs::Twist>("/" + model_name_ + "/vel_cmd", 10);
        led_pub_ = nh.advertise<std_msgs::Int32>("/" + model_name_ + "/led_mode", 10);
        
        // 创建订阅者 - 订阅手柄输入
        joy_sub_ = nh.subscribe("/joy", 10, &XboxControllerLocal::joyCallback, this);
        
        // 初始化控制参数
        max_linear_vel_ = 1.0;
        max_angular_vel_ = 1.0;
        deadband_value_ = 0.05;
        led_mode_ = 0;
        last_button_time_ = ros::Time::now();
        
        std::cout << "🎮 Xbox手柄增强LED控制器启动 - 控制车辆: " << model_name_ << std::endl;
        std::cout << "📡 发布话题:" << std::endl;
        std::cout << "  速度控制: /" << model_name_ << "/vel_cmd" << std::endl;
        std::cout << "  LED控制: /" << model_name_ << "/led_mode" << std::endl;
        std::cout << "🎮 手柄控制说明:" << std::endl;
        std::cout << "  左摇杆: 控制车辆全向移动 (前后/左右)" << std::endl;
        std::cout << "    - 上/下: 前进/后退 (linear.x)" << std::endl;
        std::cout << "    - 左/右: 左移/右移 (linear.y)" << std::endl;
        std::cout << "  A按钮: 下一个LED模式 (0-10循环)" << std::endl;
        std::cout << "  B按钮: 紧急停止车辆" << std::endl;
        std::cout << "  X按钮: 上一个LED模式" << std::endl;
        std::cout << "  Y按钮: 重置LED到常亮模式" << std::endl;
        std::cout << "  LB按钮: 快速切换到红色模式" << std::endl;
        std::cout << "  RB按钮: 快速切换到绿色模式" << std::endl;
        std::cout << "  Start按钮: 运行LED演示模式" << std::endl;
        std::cout << "💡 支持11种LED模式 (与interactive_led_control.py一致)" << std::endl;

        // 初始化LED模式
        publishLEDMode();
        printLEDModeInfo();
    }
    
    void joyCallback(const sensor_msgs::Joy::ConstPtr& msg) {
        // 处理按钮输入
        handleButtons(msg);
        
        // 处理摇杆输入
        handleSticks(msg);
    }
    
    void run() {
        ros::Rate rate(20);  // 20Hz
        
        while (ros::ok()) {
            ros::spinOnce();
            rate.sleep();
        }
    }
    
private:
    void handleButtons(const sensor_msgs::Joy::ConstPtr& msg) {
        ros::Time current_time = ros::Time::now();
        double button_debounce = 0.3;  // 按钮防抖时间

        // 确保有足够的按钮数据
        if (msg->buttons.size() < 8) {
            return;
        }

        // A按钮 (按钮0) - 下一个LED模式
        if (msg->buttons[0] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                led_mode_ = (led_mode_ + 1) % 11;  // 循环0-10，支持11种模式
                publishLEDMode();
                printLEDModeInfo();
                last_button_time_ = current_time;
            }
        }

        // B按钮 (按钮1) - 紧急停止
        if (msg->buttons[1] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                geometry_msgs::Twist stop_msg;
                stop_msg.linear.x = 0.0;
                stop_msg.linear.y = 0.0;
                stop_msg.angular.z = 0.0;
                vel_pub_.publish(stop_msg);

                std::cout << "🛑 Emergency stop!" << std::endl;
                last_button_time_ = current_time;
            }
        }

        // X按钮 (按钮2) - 上一个LED模式
        if (msg->buttons[2] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                led_mode_ = (led_mode_ - 1 + 11) % 11;  // 循环0-10，向前切换
                publishLEDMode();
                printLEDModeInfo();
                last_button_time_ = current_time;
            }
        }

        // Y按钮 (按钮3) - 重置到常亮模式
        if (msg->buttons[3] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                led_mode_ = 0;  // 重置到常亮模式
                publishLEDMode();
                printLEDModeInfo();
                std::cout << "🔄 LED reset to default mode" << std::endl;
                last_button_time_ = current_time;
            }
        }

        // LB按钮 (按钮4) - 快速切换到红色模式
        if (msg->buttons[4] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                led_mode_ = 1;  // 红色慢闪
                publishLEDMode();
                printLEDModeInfo();
                last_button_time_ = current_time;
            }
        }

        // RB按钮 (按钮5) - 快速切换到绿色模式
        if (msg->buttons[5] == 1) {
            if (current_time - last_button_time_ > ros::Duration(button_debounce)) {
                led_mode_ = 2;  // 绿色慢闪
                publishLEDMode();
                printLEDModeInfo();
                last_button_time_ = current_time;
            }
        }

        // Start按钮 (按钮7) - 运行演示模式
        if (msg->buttons[7] == 1) {
            if (current_time - last_button_time_ > ros::Duration(1.0)) {  // 更长的防抖时间
                runDemoMode();
                last_button_time_ = current_time;
            }
        }
    }
    
    void handleSticks(const sensor_msgs::Joy::ConstPtr& msg) {
        if (msg->axes.size() < 2) {
            return;
        }

        // 纯全向运动控制 - 只使用linear.x和linear.y，不需要转向
        // 左摇杆控制全向平移运动
        // axes[1] = 前进/后退 (上/下) -> linear.x
        // axes[0] = 左移/右移 (左/右) -> linear.y
        // 参考V6simple_2d_swarm_experiment.py: angular.z = 0.0 (不需要转向，直接全向移动)

        double linear_x = applyDeadband(msg->axes[1]) * max_linear_vel_;   // 前后移动
        double linear_y = applyDeadband(msg->axes[0]) * max_linear_vel_;   // 左右移动

        // 只有在有实际输入时才发布
        if (abs(linear_x) > 0.01 || abs(linear_y) > 0.01) {
            geometry_msgs::Twist vel_msg;
            vel_msg.linear.x = linear_x;
            vel_msg.linear.y = linear_y;  // 全向运动的左右移动
            vel_msg.angular.z = 0.0;      // 全向运动不需要转向

            vel_pub_.publish(vel_msg);

            // 打印控制信息（降低频率）
            static int print_counter = 0;
            if (++print_counter % 10 == 0) {  // 每10次打印一次
                std::cout << "Omnidirectional Control: linear_x=" << linear_x
                         << ", linear_y=" << linear_y << std::endl;
            }
        }
    }
    
    double applyDeadband(double value) {
        if (abs(value) < deadband_value_) {
            return 0.0;
        }
        return value;
    }

    void publishLEDMode() {
        std_msgs::Int32 led_msg;
        led_msg.data = led_mode_;
        led_pub_.publish(led_msg);
    }

    void printLEDModeInfo() {
        // LED模式信息，与interactive_led_control.py保持一致
        std::string mode_descriptions[11] = {
            "常亮模式 - 淡蓝色 - 不闪烁",
            "慢闪烁 - 红色 - 5Hz",
            "慢闪烁 - 绿色 - 5Hz",
            "慢闪烁 - 蓝色 - 5Hz",
            "慢闪烁 - 黄色 - 5Hz",
            "慢闪烁 - 紫色 - 5Hz",
            "快闪烁 - 红色 - 10Hz",
            "快闪烁 - 绿色 - 10Hz",
            "快闪烁 - 蓝色 - 10Hz",
            "快闪烁 - 黄色 - 10Hz",
            "快闪烁 - 紫色 - 10Hz"
        };

        if (led_mode_ >= 0 && led_mode_ <= 10) {
            std::cout << "💡 LED模式 " << led_mode_ << ": " << mode_descriptions[led_mode_] << std::endl;
        }
    }

    void runDemoMode() {
        std::cout << "🎬 开始LED演示模式..." << std::endl;

        // 演示序列，与interactive_led_control.py保持一致
        struct DemoStep {
            int mode;
            std::string description;
            double duration;
        };

        DemoStep demo_sequence[] = {
            {0, "常亮模式", 3.0},
            {1, "红色慢闪", 4.0},
            {6, "红色快闪", 4.0},
            {2, "绿色慢闪", 4.0},
            {7, "绿色快闪", 4.0},
            {3, "蓝色慢闪", 4.0},
            {8, "蓝色快闪", 4.0},
            {4, "黄色慢闪", 4.0},
            {9, "黄色快闪", 4.0},
            {5, "紫色慢闪", 4.0},
            {10, "紫色快闪", 4.0},
            {0, "返回常亮", 3.0}
        };

        // 启动演示线程，避免阻塞主控制循环
        std::thread demo_thread([this, demo_sequence]() {
            for (const auto& step : demo_sequence) {
                led_mode_ = step.mode;
                publishLEDMode();
                std::cout << "  演示: " << step.description << " (模式" << step.mode << ") - " << step.duration << "秒" << std::endl;

                // 使用更精确的睡眠
                std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(step.duration * 1000)));
            }
            std::cout << "🎬 演示完成！" << std::endl;
        });

        demo_thread.detach();  // 分离线程，让它独立运行
    }
    
private:
    std::string model_name_;
    ros::Publisher vel_pub_;
    ros::Publisher led_pub_;
    ros::Subscriber joy_sub_;
    
    double max_linear_vel_;
    double max_angular_vel_;
    double deadband_value_;
    int led_mode_;
    ros::Time last_button_time_;
};

int main(int argc, char** argv) {
    // 初始化ROS节点
    ros::init(argc, argv, "xbox_controller_local");
    
    try {
        XboxControllerLocal controller;
        controller.run();
    } catch (const std::exception& e) {
        ROS_ERROR("Exception: %s", e.what());
        return 1;
    }
    
    return 0;
}
